#include "video_decoder.h"
#include <chrono>
#include <thread>
#include "decoder_define.ixx"
#include "video_decoder_util.h"

namespace ai
{
    using namespace std::chrono_literals;

    VideoDecoder::VideoDecoder()
    {
        thread_ = std::thread([&]{ stateMonitorThread(); });
        decoderThread_ = std::thread([&] { decoderThread(); });
#ifdef DEV_HUAWEI
        ctxThread_ = std::thread([&]{ ctxThread(); });
#endif
    }

    VideoDecoder::~VideoDecoder()
    {
        if (dataPool_)
        {
            dataPool_->destroy();
            delete dataPool_;
            dataPool_ = nullptr;
        }

        if (devPool_)
        {
            devPool_->destroy();
            delete devPool_;
            devPool_ = nullptr;
        }
    }

    bool VideoDecoder::start(const VideoOption& op)
    {
        std::lock_guard<std::mutex> ol(decoderLock_);
        if (state_ == VCODEC_DECODER_START_STATE)
            return true;

        if (!validOption(op))
            return false;

        option_ = op;
        initDecoder();

        state_ = VCODEC_DECODER_START_STATE;
        stateCondition.notify_all();
        return true;
    }

    void VideoDecoder::stop()
    {
        {
            std::lock_guard<std::mutex> ol(decoderLock_);
            AIVCODEC_ERROR(VLOGFMT "%s stop decoder is called !", VLOGPARAM, option_.dbgInfo_.c_str());
            quit = true;
            if (state_ != VCODEC_DECODER_QUIT_STATE)
                destroyDecoder();

            state_ = VCODEC_DECODER_QUIT_STATE;
            stateCondition.notify_all();
        }

        if (decoderThread_.joinable()) {
            decoderThread_.join();
        }
        if (ctxThread_.joinable()) {
            ctxThread_.join();
        }
        if (thread_.joinable()) {
            thread_.join();
        }
    }

    bool VideoDecoder::reset(const VideoOption &option)
    {
        std::lock_guard<std::mutex> ol(decoderLock_);
        AIVCODEC_ERROR(VLOGFMT "%s reset decoder is called !", VLOGPARAM, option_.dbgInfo_.c_str());
        if (!validOption(option))
            return false;

        optionNew_ = option;
        state_ = VCODEC_DECODER_RESET_STATE;
        stateCondition.notify_all();
        return true;
    }

    bool VideoDecoder::validOption(const VideoOption& option)
    {
        if (nullptr == option.userData_ || nullptr == getDecoderState()) {
            AIVCODEC_ERROR(VLOGFMT"%s validOption option param invalid!", VLOGPARAM, option.dbgInfo_.c_str());
            return false;
        }

        if (option_.processId_ < 1) {
            AIVCODEC_ERROR(VLOGFMT "%s process id is invalid, start from 1!", VLOGPARAM, option.dbgInfo_.c_str());
            return false;
        }

        if (option_.processId_ * option_.oneChannelCount_ > VDEC_MAX_CHN_NUM) {
            AIVCODEC_ERROR(VLOGFMT "%s Invalid one channel count!", VLOGPARAM, option.dbgInfo_.c_str());
            return false;
        }

        if (option.oWidth_ < MIN_VIDEO_WIDTH || option.oWidth_ > MAX_VIDEO_WIDTH) {
            AIVCODEC_ERROR(VLOGFMT "%s output width %d is invalid, [%d,%d] is valid!",
                VLOGPARAM, option.dbgInfo_.c_str(), option.oWidth_, MIN_VIDEO_WIDTH, MAX_VIDEO_WIDTH);
            return false;
        }

        if (option.oHeight_ < MIN_VIDEO_HEIGHT || option.oHeight_ > MAX_VIDEO_HEIGHT) {
            AIVCODEC_ERROR(VLOGFMT "%s output height %d is invalid, [%d,%d] is valid!",
                VLOGPARAM, option.dbgInfo_.c_str(), option.oHeight_, MIN_VIDEO_HEIGHT, MAX_VIDEO_HEIGHT);
            return false;
        }

        return codecSupported();
    }

    bool VideoDecoder::sendPacket(const VideoPacket &packet)
    {
        if (!decodeRunning())
        {
            AIVCODEC_ERROR("sendPacket failed,  channel:%d not ready", option_.channelId_);
            return false;
        }

        std::unique_lock lk(packetLock_);
        packets_.push_back(packet.frameNum_);
        if (packets_.size() > MAX_FRAME_SIZE)
        {
            int64_t frameId = packets_.front();
            packets_.pop_front();
            lk.unlock();
            auto now = std::chrono::steady_clock::now();
            if (now - lastLogTime > std::chrono::minutes (5))
            {
                AIVCODEC_WARN("sendPacket packets is full, channel:%d, size:%d, drop frame:%d", option_.channelId_, packets_.size(), frameId);
                lastLogTime = now;
            }
            dropVideoFrame(frameId);
        }

        return true;
    }

    void VideoDecoder::dropVideoFrame(int64_t frameId)
    {
        if (option_.eventCallback_)
            option_.eventCallback_(VCODEC_DROP_FRAME, option_.userData_, reinterpret_cast<void *>(frameId));
    }

    /**
     * @brief 从packets取出帧序号
     * @return frameId
     */
    int64_t VideoDecoder::getPacketData()
    {
        std::lock_guard lock(packetLock_);
        if (packets_.empty())
            return -1;

        int64_t frameId = packets_.front();
        packets_.pop_front();

        return frameId;
    }

    BufferDesc* acquireBufferWithTimeout(VideoDataPool* pool, std::chrono::milliseconds timeout)
    {
        if (!pool)
            return nullptr;

        auto bufDesc = static_cast<BufferDesc*>(pool->acquire());
        if (bufDesc)
            return bufDesc;

        const auto startTime = std::chrono::steady_clock::now();
        while (std::chrono::steady_clock::now() - startTime < timeout)
        {
            std::this_thread::sleep_for(std::chrono::milliseconds(5));
            bufDesc = static_cast<BufferDesc*>(pool->acquire());
            if (bufDesc)
                return bufDesc;
        }
        return nullptr;
    }

    void releaseBuffer(BufferDesc* bufDesc,  DeviceFramePool* devicePool, VDecDataPool* dataPool)
    {
        if (bufDesc)
        {
            if (bufDesc->frame_ && devicePool)
            {
                devicePool->release(bufDesc->frame_);
                bufDesc->frame_ = nullptr;
            }
            if (dataPool)
                dataPool->release(bufDesc);
        }
        else
        {
            AIVCODEC_ERROR("releaseBuffer bufDesc is null");
        }
    }

    bool VideoDecoder::decodePacket()
    {
        if (!devPool_ || !dataPool_)
        {
            AIVCODEC_ERROR("decodePacket pool is null");
            return false;
        }

        int64_t frameId = getPacketData();
        if (-1 == frameId)
            return false;

        // 先强制回收过期BufferDesc缓存, BufferDesc下的设备内存也要归还
        auto oldestBuffer = static_cast<BufferDesc*>(dataPool_->forceRecycleOldBuffer(frameId));
        if (oldestBuffer)
        {
            releaseBuffer(oldestBuffer, devPool_, nullptr);
            oldestBuffer = nullptr;
        }

        auto bufDesc = acquireBufferWithTimeout(dataPool_, std::chrono::milliseconds(40));
        if (!bufDesc)
        {
            dropVideoFrame(frameId);
            AIVCODEC_ERROR("decodePacket failed, dataPool_ is full index:%lld", frameId);
            return false;
        }

        devPool_->forceRecycleOldBuffer(frameId);
        bufDesc->frame_ = static_cast<DeviceFrame*>(devPool_->acquire());
        if (!bufDesc->frame_)
        {
            AIVCODEC_ERROR("decodePacket failed, devPool_ is full index:%lld, bufDesc %p, channel %ld", frameId, bufDesc, option_.channelId_);
            releaseBuffer(bufDesc, devPool_, dataPool_);
            dropVideoFrame(frameId);
            return false;
        }
        bufDesc->frameNum_ = frameId;
        bufDesc->frame_->frame_num = frameId;

        bool useDeviceMem = true;
#ifdef DEV_CAMBRICON
        useDeviceMem = false;
#endif

        if (!getVideoGstFrameBuffer(option_.deviceId_, frameId, &bufDesc->pkt_, option_.userData_, useDeviceMem))
        {
            AIVCODEC_ERROR("decodePacket get gst frame info failed, index:%lld, bufDesc %p, channel %ld", frameId, bufDesc, option_.channelId_);
            releaseBuffer(bufDesc, devPool_, dataPool_);
            dropVideoFrame(frameId);
            return false;
        }

        // 将帧数据发送给到解码器进行解码
        if (!decodePacketImpl(bufDesc))
        {
            AIVCODEC_ERROR("decodePacket failed, frameId:%ld, bufDesc %p, channel %ld", frameId,  bufDesc, option_.channelId_);
            releaseBuffer(bufDesc, devPool_, dataPool_);
            dropVideoFrame(frameId);
            return false;
        }
        codecStateStepCode(getDecoderState()->codecSend_);
        return true;
    }

    void VideoDecoder::decoderThread()
    {
        while (!quit)
        {
#ifdef DEV_HUAWEI
            if (decodeRunning())
                initHWDecoderContext();
#endif
            while (decodeRunning())
            {
                if (!decodePacket())
                {
                    std::this_thread::sleep_for(10ms);
                }
                std::this_thread::sleep_for(2ms);
            }

            std::this_thread::sleep_for(50ms);
        }

        AIVCODEC_INFO( "decoderThread exit!");
    }

    bool VideoDecoder::initDecoder()
    {
        if (!setDecoderContext())
        {
            AIVCODEC_ERROR("initDecoder failed !");
            return false;
        }

#ifdef DEV_HUAWEI
        frameCtl_.setWaitTimeMs(33);
        frameCtl_.reset();
        VcodecException::addListener(this);
#endif
        frameNum_ = 0;
        initCodecStatis();
        return true;
    }

    bool VideoDecoder::resetDecoder()
    {
        AIVCODEC_WARN("start reset decoder, channel:%d !", option_.channelId_);
        cleanDecoderFrame();
        destroyDecoder();
        optionNew_.channelId_ = option_.channelId_;
        option_ = optionNew_;

        if (!initDecoder())
        {
            // 初始化失败，保持当前状态为 RESET，等待下一轮重试
            AIVCODEC_ERROR("resetDecoder failed, will retry later. channel:%d !", option_.channelId_);

            // 防止 stateMonitorThread 进入忙循环，占用 CPU
            std::this_thread::sleep_for(std::chrono::milliseconds(200));
            return false;
        }

        // 初始化成功，更新状态为 START
        state_ = VCODEC_DECODER_START_STATE;

        AIVCODEC_INFO("reset decoder successfully, channel:%d !", option_.channelId_);
        return true;
    }

    void VideoDecoder::stateMonitorThread()
    {
        while (!quit)
        {
            std::unique_lock lock(decoderLock_);
            stateCondition.wait_for(lock, std::chrono::milliseconds(50), [this]() {
                return quit || state_ == VCODEC_DECODER_RESET_STATE || state_ == VCODEC_DECODER_QUIT_STATE;
            });

            if (quit || state_ == VCODEC_DECODER_QUIT_STATE)
            {
                AIVCODEC_INFO("stateMonitorThread, channel:%d  exit ...", option_.channelId_);
                break;
            }

            if (state_ == VCODEC_DECODER_RESET_STATE)
            {
                AIVCODEC_INFO("stateMonitorThread, channel:%d reset  ...", option_.channelId_);
                lock.unlock();
                resetDecoder();
            }
        }
    }


    void VideoDecoder::ctxThread()
    {
        while (!quit)
        {
            bool hwReset = true;
            while (decodeRunning())
            {
                if (hwReset)
                {
                    // 重置解码器后，需要设置context(华为平台)
                    initHWDecoderContext();
                    hwReset = false;
                }

                handlerDecoderFrame();
                std::this_thread::sleep_for(5ms);
            }
#ifdef HUAWEI_310
                // 华为310在停止解码时，销毁通道接口是一个同步接口，需要等待所有帧处理完，这里需要继续触发回调处理
                while (!clearHWDecoderFrame()) {
                    handlerDecoderFrame();
                }
#endif
            std::this_thread::sleep_for(5ms);
        }
        AIVCODEC_INFO("ctxThread, channel:%d  exit ...", option_.channelId_);
    }

    bool VideoDecoder::decodeRunning()
    {
        std::unique_lock lock(decoderLock_);
        return (state_ == VCODEC_DECODER_START_STATE && !quit);
    }

    void VideoDecoder::cleanDecoderFrame()
    {
        auto ai_decoder = static_cast<GstVideoDecoder*>(option_.userData_);
        if (!ai_decoder)
            return;

        std::lock_guard ol(packetLock_);
        while (!packets_.empty())
        {
            auto frame = reinterpret_cast<GstVideoCodecFrame*>(packets_.front());
            if (frame)
                gst_video_decoder_drop_frame(ai_decoder, frame);

            packets_.pop_front();
        }
    }

    void VideoDecoder::onExceptionCallback(const uint32_t taskId, const uint32_t streamId, const uint32_t threadId, const uint32_t deviceId)
    {
        AIVCODEC_ERROR("Ai core exception occurred and caught, taskId:%u, streamId:%u, threadId:%d, deviceId:%d", taskId, streamId, threadId, deviceId);

        if (0xffffffff == taskId || 0xffffffff == streamId)
        {
            if (option_.eventCallback_)
                option_.eventCallback_(VCODEC_ERROR_EXCEPTION, option_.userData_, nullptr);
        }
    }

    void VideoDecoder::releaseDeviceFrame(void* devFrame)
    {
        if (devPool_)
            devPool_->release(devFrame);
    }

    void VideoDecoder::initCodecStatis()
    {
        initCodecState(getDecoderState()->codecSend_, VCODEC_LOG_DEBUG, "decode send: ");
        initCodecState(getDecoderState()->codecRecv_, VCODEC_LOG_DEBUG, "decode receive: ");
    }

    bool VideoDecoder::clearDataPool()
    {
        if (dataPool_ && devPool_)
        {
            std::list<void*> inputList;
            dataPool_->getAndClearTmpList(inputList);

            for (const auto & pos : inputList)
            {
                const auto desc = static_cast<BufferDesc *>(pos);
                if (!desc)
                    continue;

                if (desc->frame_)
                {
                    devPool_->release(desc->frame_);
                    desc->frame_ = nullptr;
                }
                dataPool_->release(desc);
            }
        }

        return true;
    }

    void VideoDecoder::setDecoderState(VIDEO_DECODER_STATE state)
    {
        std::unique_lock lock(decoderLock_);
        state_ = state;
        stateCondition.notify_all();
    }

}


#if defined(CAMBRICON_MLU220) || defined(CAMBRICON_MLU270)
    #include "cambricon/mlu2x/mlu200_video_decoder.h"
#elif defined(CAMBRICON_MLU370)
    #include "cambricon/mlu3x/mlu300_video_decoder.h"
#elif defined(HUAWEI_310)
    #include "huawei/v1/310ai_video_decoder.h"
#elif defined(HUAWEI_310P)
    #include "huawei/v2/310pai_video_decoder.h"
#endif

ai::VideoDecoder* createVideoDecoder()
{
    ai::VideoDecoder* decoder = nullptr;
#ifdef DEV_CAMBRICON
    #if defined(CAMBRICON_MLU220) || defined(CAMBRICON_MLU270)
        decoder = new ai::Mlu200VideoDecoder();
    #elif defined(CAMBRICON_MLU370)
        decoder = new ai::Mlu300VideoDecoder();
    #endif
#elif defined DEV_HUAWEI
    #ifdef HUAWEI_310P
        decoder = new ai::Acl310PAiVideoDecoder();
    #elif defined(HUAWEI_310)
        decoder = new ai::Acl310AiVideoDecoder();
    #endif
#endif
    return decoder;
}
