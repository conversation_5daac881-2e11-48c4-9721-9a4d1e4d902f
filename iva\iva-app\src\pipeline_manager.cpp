#include <gst/gst.h>
#include <thread>
#include <future>
#include <chrono>
#include <mutex>
#include <set>
#include <map>
#include <queue>
#include <condition_variable>
#include "pipeline_manager.h"
#include "pipeline_factory.h"
#include "ivaconfig.hpp"
#include "ivacommon.h"
#include "ivameta.h"
#include "log.h"

#ifdef NVIDIA
#include <gst-nvmessage.h>
#ifdef USE_VIDEO_RECORD
#include "video_record.h"
#endif// USE_VIDEO_RECORD
#endif// NVIDIA

#ifdef ENABLE_IMAGE_ANALYSER
#include "image_analyser.h"
#include "detect_config.hpp"
#endif// ENABLE_IMAGE_ANALYSER

namespace iva::pipeline
{
    using namespace std;
	int processId = 0;                    //!< 进程ID
	int deviceId = 0;                     //!< gpu 设备ID
	int totalChannelSize = 1;            //!< iva可推理的通道总数
	GstElement* mainPipeline = nullptr;

    namespace
    {
		#define RTMP_REQUEST_INTERVAL  5     //   结构化请求时间间隔
        int currentRtmpStreamID = -1;         //!< rtmp推流streamID
        guint recordBeforeTime = 2;           //!< 相对于当前时刻之前的时间
        guint recordAfterTime = 3;            //!< 当前目标时刻后继续录制时间

        atomic_bool running = false;
        std::thread pipelineLoopThread;

        GMainLoop* mainLoop = nullptr;

        atomic_bool modelLoadFailed = false;

        std::function<void()> onPipelineInitedExt;  //!< pipeline初始化完成外部回调函数

        int latestRequestRtmpIndex = -1;            //!< 最新的结构化请求通道
        atomic_bool processingRtmpRequest = false;  //!< 是否正在处理结构化通道请求
        std::chrono::steady_clock::time_point lastRtmpRequestTime;  //!< 上一次结构化请求时间
        atomic_bool isRtmpRequestExpired = false;   //!< 上一次结构化请求是否超时

        // 解码器重置状态管理
        std::mutex resetMutex;                      //!< 重置操作互斥锁
        std::set<int> resettingChannels;            //!< 正在重置的通道集合
        std::map<int, std::chrono::steady_clock::time_point> resetStartTimes; //!< 重置开始时间
        const std::chrono::seconds RESET_TIMEOUT{20}; //!< 重置超时时间
    }

    /**
     * @brief 消息总线回调处理
     */
    gboolean busCall(GstBus * bus, GstMessage * msg, gpointer data);

    /**
     * @brief rtmpsink和fakesink交换
     * @note  在不需要推流时，ivaplugin插件的数据送到fakesink，当需要推流时，将fakesin切换为rtmpsink，以节省资源
     */
    static GstPadProbeReturn onRtmpFakeSinkSwitch(GstPad * pad, GstPadProbeInfo * info, gpointer user_data);

    /**
     * @brief 重置裁剪模块
     * @note  pipeline运行时，设置裁剪参数会失败(ds5.0 bug)，需要重置裁剪模块
     */
    static GstPadProbeReturn onResetVideoCrop(GstPad * pad, GstPadProbeInfo * info, gpointer user_data);

    /**
     * @brief 对解码模块发送EOS事件
     * @note  该探针发送EOS到rtph264_depay的sinkpad上，当rtph264_depay的src pad的eos_event探针收到EOS事件后，即表示缓冲数据全部处理完
     */
    static GstPadProbeReturn onResetDecoderSendEos(GstPad * pad, GstPadProbeInfo * info, gpointer user_data);

    /**
     * @brief 重置解码模块
     * @note  接收到EOS事件后，重置rtph264_depay插件
     */
    static GstPadProbeReturn onResetDecoderEosEvent(GstPad * pad, GstPadProbeInfo * info, gpointer user_data);

    /**
     * @brief 管道初始化后流程(开始收流， 通知FVM初始化)
     */
    static gboolean onPipelineInited(gpointer data);

#if defined(USE_VIDEO_RECORD)
    static void linkRecordBin(guint index, GstElement* srcBin, GstElement* tee);
#endif

    /**
     * @brief 开始接收流数据
     * @param index 插件序号，对应通道号-1
     */
    void startReceiveStream(int index);

    /**
     * @brief rtmp和fake sink元件切换处理
     */
    void switchRtmpFakeSink(int index);

    /**
     * @brief 初始化gstreamer, 注册pipeline始化完成回调
     * @param channelSize 支持的通道数量
     * @param processID   进程ID
     * @param deviceID    gpu设备ID
     * @param callback    pipeline初始化完成外部回调函数
     */
    void init(int channelSize, int processID, int deviceID, const std::function<void()>& callback)
    {
        totalChannelSize = channelSize;
		processId = processID;
        deviceId = deviceID;
        // gstreamer initialization
        gst_init(NULL, NULL);
        onPipelineInitedExt = callback;
    }

    /**
     * @brief 启动pipeline
     */
    void start()
    {
        // start pipeline async
        if (!running)
        {
            running = true;
            pipelineLoopThread = std::thread([&] {process(); });
        }
    }

    /**
     * @brief 停止pipeline
     */
    void stop()
    {
        if (running)
        {
			running = false;
            //gst_element_set_state(mainPipeline, GST_STATE_NULL);
			if(mainLoop)
				g_main_loop_quit(mainLoop);

            if(pipelineLoopThread.joinable())
				pipelineLoopThread.join();
        }
    }

    /**
     * @brief 获取pipeline运行状态
     */
    bool isRunning()
    {
        return running;
    }
    /**
     * @brief 所有通道的pipeline创建、连接、启动
     */
    int process()
    {
        GstElement *mux = NULL, *demux = NULL;

        auto loop = mainLoop = g_main_loop_new(NULL, FALSE);
        auto pipeline = mainPipeline = gst_pipeline_new("iva-pipeline");

        int defaultChannelSize = totalChannelSize;
		if (defaultChannelSize <= 0)
		{
			IVA_LOG_WARN("pipeline's channel size is negative, fall back to one");
			defaultChannelSize = 1;
		}
#ifdef CAMBRICON	// TODO 寒武纪暂适配1路
		defaultChannelSize = 1;
#endif // CAMBRICON

        // create mux [Default model]
        mux = gst_element_factory_make(AI_STREAMMUX, "iva-streammux");
		GST_ELEMENT_CHECK1(mux, "iva-streammux");
		gst_bin_add(GST_BIN(pipeline), mux);

#ifdef NVIDIA
        g_object_set(G_OBJECT(mux), "batch-size", defaultChannelSize,
	#ifndef USE_NEW_STREAMMUX
            "gpu-id", deviceId,
			"width", SETTINGS->videoWidth(), "height", SETTINGS->videoHeight(),
			"nvbuf-memory-type", 0,
			"live-source", TRUE,
			"batched-push-timeout", SETTINGS->batchTimeout(),
			"interpolation-method", SETTINGS->interpolation(),
	#else
			"config-file-path", "/data/opt/models/config_mux.txt",
	#endif // !USE_NEW_STREAMMUX
            NULL);
#elif CAMBRICON // TODO 寒武纪暂适配1路 4batch(时序)
		g_object_set(G_OBJECT(mux), "batch-size", 4* (SETTINGS->vehicleInferInterval()+1), NULL);
#elif HUAWEI
		g_object_set(G_OBJECT(mux), "batch-size", totalChannelSize, NULL);
#endif // NVIDIA

        // create demux
        demux = gst_element_factory_make(AI_STREAMDEMUX, "iva-streamdemux");
		GST_ELEMENT_CHECK1(demux, "iva-streamdemux");
        gst_bin_add(GST_BIN(pipeline), demux);

		auto coreBin = createCoreBin(0);
		GST_ELEMENT_CHECK1(coreBin, "core bin");
		gst_bin_add(GST_BIN(pipeline), coreBin);

        // source bins  -> core bin -> sink bins
        for (int i = 0; i < totalChannelSize; ++i)
        {
            // create source bin i
            auto srcBin = createSourceBin(i);
			GST_ELEMENT_CHECK1(srcBin, "source bin");
            gst_bin_add(GST_BIN(pipeline), srcBin);

			// create sink bin i
			auto sinkBin = createSinkBin(i);
			GST_ELEMENT_CHECK1(sinkBin, "sink bin");
			gst_bin_add(GST_BIN(pipeline), sinkBin);

			CHECK_IF_MSG1(link_element_to_streammux_sink_pad(mux, srcBin, i), "streammux link error");
			CHECK_IF_MSG1(link_element_to_demux_src_pad(demux, sinkBin, i), "streamdemux link error");
        }

		GST_ELEMENT_LINK_MANY_CHECK("main pipe", mux, coreBin, demux);

        // listen bus message
        auto bus = gst_pipeline_get_bus (GST_PIPELINE (pipeline));
        auto bus_watch_id = gst_bus_add_watch (bus, busCall, loop);
        gst_object_unref (bus);

        // start run
        gst_element_set_state (pipeline, GST_STATE_PLAYING);
        gst_element_get_state(pipeline, 0, 0, GST_SECOND * 10);

        g_timeout_add_seconds(3, onPipelineInited, NULL);

        // run in loop
        g_print ("[IVA]==Running...\n");
		if(running)
			g_main_loop_run (loop);

        // stop run
        g_print ("Returned, stopping playback\n");
        gst_element_set_state (pipeline, GST_STATE_NULL);
        g_print ("Deleting pipeline\n");
        gst_object_unref (GST_OBJECT (pipeline));

        g_source_remove(bus_watch_id);
        g_main_loop_unref(loop);

        running = false;
        return 0;
    }

    /**
     * @brief 管道初始化后流程(开始收流， 通知FVM初始化)
     */
    static gboolean onPipelineInited(gpointer data)
    {
        if (modelLoadFailed)
            return false; // 模型加载失败， 暂停流程！

        for (int i = 0; i < totalChannelSize; ++i)
        {
            pipeline::startReceiveStream(i);
        }

        if (onPipelineInitedExt != nullptr)
            onPipelineInitedExt();

        return false;
    }

    /**
     * @brief 配置通道日志是否开启输出
     */
    void printLog(int index, bool enable)
    {
        // only work when pipeline is running
        if (running)
        {
            gchar elem_name[50];
            for(int i = 0; i < totalChannelSize; ++i)
            {
                g_snprintf (elem_name, sizeof(elem_name), "sink_bin%d", i);
                auto sink_bin = gst_bin_get_by_name(GST_BIN(mainPipeline), elem_name);
                g_snprintf (elem_name, sizeof(elem_name), "sink_ivaplugin1_%d", i);
                auto ivaplugin1 = gst_bin_get_by_name(GST_BIN(sink_bin), elem_name);

                if(index == -1)  // all
                {
                    g_object_set(G_OBJECT(ivaplugin1), "print-process-log", false, NULL);
                    if(i == totalChannelSize - 1)
                    {
                        g_print("[IVA]==stop print process log for all channels\n");
                    }
                }
                else
                {
                    if(index == i)
                    {
                        g_object_set(G_OBJECT(ivaplugin1), "print-process-log", true, NULL);
                        g_print("[IVA]==start print process log for channel_index:[%d]\n", i);
                        break;
                    }
                }
            }
        }
    }

    /**
     * @brief 打印通道状态
     */
    void printStatus()
    {
        char status[255];
        sprintf(status, "[IVA]==status: \n     [channel size]:%d\n"
                        "     [streaming]:%d\n",
                totalChannelSize, currentRtmpStreamID);
        IVA_LOG_INFO(status)
    }

    /**
     * @brief 重置裁剪参数
     * @param index 插件序号，对应通道号-1
     * @param x,y,w,h 裁剪参数
     * @param fullscreen 是否全屏
     */
    void setVideoCrop(int index, int x, int y, int w, int h, bool fullscreen)
    {
        if (index > totalChannelSize)
            return;

        if (running)  // only work when pipeline is running
        {
            gchar elem_name[50];

            g_snprintf(elem_name, sizeof(elem_name), "sink_bin%d", index);
            auto sink_bin = gst_bin_get_by_name(GST_BIN(mainPipeline), elem_name);

            g_snprintf(elem_name, sizeof(elem_name), "src_bin%d", index);
            auto src_bin = gst_bin_get_by_name(GST_BIN(mainPipeline), elem_name);

            g_snprintf(elem_name, sizeof(elem_name), "crop_conv%d", index);
            auto crop_conv = gst_bin_get_by_name(GST_BIN(src_bin), elem_name);

            gchar *src_crop;
            g_object_get(G_OBJECT(crop_conv), "src-crop", &src_crop, NULL);

            gchar new_crop[32] = {};
            g_snprintf(new_crop, 31, "%u:%u:%u:%u", x, h, w, h);
            // ds5.0 bug
            // see https://forums.developer.nvidia.com/t/bug-in-property-read-src-crop-in-nvvideoconvert/164616/4
            //if (w > 10 && h > 10 && strcmp(src_crop, new_crop))
			if ((strcmp(src_crop, "0:0:0:0") && fullscreen) || (!fullscreen && w > 10 && h > 10 && strcmp(src_crop, new_crop)))
            {
                IVA_LOG_INFO("src_crop: {} {}", index, src_crop);
                g_free(src_crop);
                int *usrdata = new int[5]{index, x, y, w, h};
                GstPad *conv_src_pad = gst_element_get_static_pad(crop_conv, "src");
                gst_pad_add_probe(conv_src_pad, GST_PAD_PROBE_TYPE_BLOCK_DOWNSTREAM, onResetVideoCrop, usrdata, NULL);
                gst_object_unref(conv_src_pad);
            }
        }
    }

	static GstPadProbeReturn onResetDecoderSource(GstPad* pad, GstPadProbeInfo* info, gpointer user_data)
	{
		gchar elem_name[50];
		GstPad* srcpad, * mux_sink_pad;
		int index = GPOINTER_TO_INT(user_data);
		IVA_LOG_INFO("Decoder {} reset start", index);

    	// 检查是否已经在重置中
	    {
			std::lock_guard lock(resetMutex);
			if (resettingChannels.find(index) == resettingChannels.end())
			{
				IVA_LOG_WARN("Decoder {} reset was cancelled or already completed", index);
				return GST_PAD_PROBE_REMOVE;
			}
	    }

		g_snprintf(elem_name, sizeof(elem_name), "src_bin%d", index);
		GstElement* src_bin = gst_bin_get_by_name(GST_BIN(mainPipeline), elem_name);
    	if (!src_bin)
    	{
    		IVA_LOG_ERROR("Failed to find src_bin{} during reset", index);
    		// 清除重置标记
    		std::lock_guard<std::mutex> lock(resetMutex);
    		resettingChannels.erase(index);
    		resetStartTimes.erase(index);
    		return GST_PAD_PROBE_DROP;
    	}
		gst_pad_remove_probe(pad, GST_PAD_PROBE_INFO_ID(info));

		auto mux = gst_bin_get_by_name(GST_BIN(mainPipeline), "iva-streammux");
    	if (!mux)
    	{
    		IVA_LOG_ERROR("Failed to find iva-streammux during reset");
    		gst_object_unref(src_bin);

    		std::lock_guard lock(resetMutex);
    		resettingChannels.erase(index);
    		resetStartTimes.erase(index);
    		return GST_PAD_PROBE_DROP;
    	}

		srcpad = gst_element_get_static_pad(src_bin, "src");
		mux_sink_pad = gst_pad_get_peer(srcpad);
		if (!gst_pad_unlink(srcpad, mux_sink_pad))
		{
			NVGSTDS_ERR_MSG_V("unlink mux error");
			// 清除重置标记
			std::lock_guard<std::mutex> lock(resetMutex);
			resettingChannels.erase(index);
			resetStartTimes.erase(index);
			return GST_PAD_PROBE_DROP;
		}
		if (srcpad) {
			gst_object_unref(srcpad);
		}
		IVA_LOG_INFO("Decoder {} reset unlinking", index);

		// 异步停止旧的 source bin，避免死锁
		gst_element_set_state(src_bin, GST_STATE_NULL);

		// 给解码器线程更多时间来正常退出，避免死锁
		std::this_thread::sleep_for(std::chrono::milliseconds(200));

		// 尝试等待状态变化，但设置较短的超时时间
		GstState state, pending;
		GstStateChangeReturn ret = gst_element_get_state(src_bin, &state, &pending, GST_MSECOND * 500);
		if (ret == GST_STATE_CHANGE_FAILURE) {
		    IVA_LOG_WARN("Decoder {} state change failed during reset, forcing removal", index);
		}

		gst_bin_remove(GST_BIN(mainPipeline), src_bin);
		gst_object_unref(src_bin);

		// 创建新的 source bin
		GstElement* new_src_bin = createSourceBin(index);
		if (!new_src_bin)
		{
			NVGSTDS_ERR_MSG_V("Failed to create '%s'", elem_name);
			// 清除重置标记
			std::lock_guard<std::mutex> lock(resetMutex);
			resettingChannels.erase(index);
			resetStartTimes.erase(index);
			return GST_PAD_PROBE_DROP;
		}
		gst_bin_add(GST_BIN(mainPipeline), new_src_bin);
		srcpad = gst_element_get_static_pad(new_src_bin, "src");

		IVA_LOG_INFO("Decoder {} reset creating", index);

		// 逐步设置状态，确保正确的状态转换
		gst_element_set_state(new_src_bin, GST_STATE_READY);
		gst_element_get_state(new_src_bin, nullptr, nullptr, GST_SECOND * 2);

		// 重新连接到 mux
		srcpad = gst_element_get_static_pad(new_src_bin, "src");
		if(gst_pad_link(srcpad, mux_sink_pad) != GST_PAD_LINK_OK)
			NVGSTDS_ERR_MSG_V("link mux error");

		if (mux_sink_pad) {
			gst_object_unref(mux_sink_pad);
		}
		if (srcpad) {
			gst_object_unref(srcpad);
		}
		IVA_LOG_INFO("Decoder {} reset linking", index);

		// 设置为 PAUSED 状态，触发格式协商
		gst_element_set_state(new_src_bin, GST_STATE_PAUSED);
		gst_element_get_state(new_src_bin, nullptr, nullptr, GST_SECOND * 2);

		// 配置 UDP 源
		startReceiveStream(index);

		// 最后设置为 PLAYING 状态
		gst_element_set_state(new_src_bin, GST_STATE_PLAYING);
		gst_element_get_state(new_src_bin, nullptr, nullptr, GST_SECOND * 2);
		gst_element_set_state(mainPipeline, GST_STATE_PLAYING);

		//GST_DEBUG_BIN_TO_DOT_FILE(GST_BIN(mainPipeline), GST_DEBUG_GRAPH_SHOW_ALL, "pipeline");
		IVA_LOG_INFO("Decoder {} reset finished", index);

		// 清除重置标记
	    std::lock_guard<std::mutex> lock(resetMutex);
	    resettingChannels.erase(index);
	    resetStartTimes.erase(index);
		return GST_PAD_PROBE_OK;
	}

    /**
     * 重置解码模块
     * @param index 插件序号，对应通道号-1
     */
    void resetDecoder(int index)
    {
    	std::cout << "resetDecoder " << index << std::endl;
        if (!running)
            return;

        // 检查是否已经在重置中，防止并发重置
        {
            std::lock_guard<std::mutex> lock(resetMutex);
            auto now = std::chrono::steady_clock::now();

            // 检查是否有超时的重置操作，清理它们
            auto it = resetStartTimes.begin();
            while (it != resetStartTimes.end())
            {
                if (now - it->second > RESET_TIMEOUT)
                {
                    IVA_LOG_WARN("Decoder {} reset timeout, clearing reset flag", it->first);
                    resettingChannels.erase(it->first);
                    it = resetStartTimes.erase(it);
                }
                else
                {
                    ++it;
                }
            }

            if (resettingChannels.find(index) != resettingChannels.end())
            {
                IVA_LOG_WARN("Decoder {} reset already in progress, ignoring duplicate request", index);
                return;
            }
            // 标记该通道正在重置并记录开始时间
            resettingChannels.insert(index);
            resetStartTimes[index] = now;
        }

        gchar elem_name[50];
        g_snprintf(elem_name, sizeof(elem_name), "src_bin%d", index);
        GstElement* src_bin = gst_bin_get_by_name(GST_BIN(mainPipeline), elem_name);

		if (src_bin)
		{
			IVA_LOG_WARN("Decoder {} reset request", index);
#ifdef HUAWEI
			GstPad* blockpad = gst_element_get_static_pad(src_bin, "src");
			gst_pad_add_probe(blockpad, GST_PAD_PROBE_TYPE_BLOCKING, onResetDecoderSource, GINT_TO_POINTER(index), NULL);
#else
		    g_snprintf(elem_name, sizeof(elem_name), "rtpdepay_capsfilter%d", index);
		    GstElement* rtpdepay_capsfilter = gst_bin_get_by_name(GST_BIN(src_bin), elem_name);

		    GstPad * blockpad = gst_element_get_static_pad(rtpdepay_capsfilter, "src");
		    gst_pad_add_probe(blockpad, GST_PAD_PROBE_TYPE_BLOCK_DOWNSTREAM, onResetDecoderSendEos, src_bin, NULL);
		    IVA_LOG_WARN("Decoder {} reset", index);
#endif
			gst_object_unref(blockpad);
		}
		else
		{
		    // 如果找不到 src_bin，清除重置标记
		    std::lock_guard<std::mutex> lock(resetMutex);
		    resettingChannels.erase(index);
		    resetStartTimes.erase(index);
		}
    }

    /**
     * @brief 设置通道的检测状态
     * @param index 插件序号，对应通道号-1
     */
    void setDetectStatus(int index, int videoId, std::optional<int> presetId, IVAChannelState detect)
    {
        if (index > totalChannelSize)
            return;

        if (running)
        {
            gchar elem_name[50];
            g_snprintf (elem_name, sizeof(elem_name), "sink_bin%d", index);
            auto sink_bin = gst_bin_get_by_name(GST_BIN(mainPipeline), elem_name);

            g_snprintf(elem_name, sizeof(elem_name), "sink_image_analyser_%d", index);
            auto image_analyser = gst_bin_get_by_name(GST_BIN(sink_bin), elem_name);

            g_snprintf (elem_name, sizeof(elem_name), "sink_ivaplugin1_%d", index);
            auto ivaplugin = gst_bin_get_by_name(GST_BIN(sink_bin), elem_name);

            // 更新ivaplugin1 相关信息
            g_object_set(G_OBJECT(ivaplugin), "video-id", videoId, NULL);
            if (presetId.has_value())
                g_object_set(G_OBJECT(ivaplugin), "preset-id", presetId.value(), NULL);
            g_object_set(G_OBJECT(ivaplugin), "detecting", detect, NULL);

            // 更新imageanalyser 相关信息
            g_object_set(G_OBJECT(image_analyser), "video-id", videoId, NULL);
            if (presetId.has_value())
                g_object_set(G_OBJECT(image_analyser), "preset-id", presetId.value(), NULL);
            g_object_set(G_OBJECT(image_analyser), "detecting", detect, NULL);
        }
    }

    /**
     * @brief 开始接收流数据
     * @param index 插件序号，对应通道号-1
     */
    void startReceiveStream(int index)
    {
        if (index > totalChannelSize)
            return;

        gchar elem_name[50];
        g_snprintf(elem_name, sizeof(elem_name), "src_bin%d", index);
        auto src_bin = gst_bin_get_by_name(GST_BIN(mainPipeline), elem_name);

        g_snprintf(elem_name, sizeof(elem_name), "udp_src%d", index);
        auto udp_src = gst_bin_get_by_name(GST_BIN(src_bin), elem_name);

        gst_element_set_state(udp_src, GST_STATE_NULL);
        gst_element_get_state(udp_src, 0, 0, GST_SECOND * 10);

        guint port = 20000 + (index + (processId -1) * totalChannelSize) * 2;
        g_object_set(G_OBJECT(udp_src), "port", port, NULL);
        gst_element_set_state(udp_src, GST_STATE_PLAYING);
    }

    /**
     * @brief 设置所有通道的偏移归位时间
     * @param offsetReturnTime 偏移归位时间
     */
    void setOffsetReturnTime(int offsetReturnTime)
    {
        if (!running)
            return;

        for(int i = 0; i < totalChannelSize; ++i)
        {
            gchar elem_name[50];
            g_snprintf (elem_name, sizeof(elem_name), "sink_bin%d", i);
            auto sink_bin = gst_bin_get_by_name(GST_BIN(mainPipeline), elem_name);

            // ivaplugin1
            // Auto reseting time
            g_snprintf(elem_name, sizeof(elem_name), "sink_ivaplugin1_%d", i);
            auto ivaplugin = gst_bin_get_by_name(GST_BIN(sink_bin), elem_name);
            g_object_set(G_OBJECT(ivaplugin), "autoResetTime", offsetReturnTime, NULL);
        }
    }

    /**
     * @brief reset channel
     */
    void resetChannel(int index)
    {
        if (index < -1 || index >= totalChannelSize)
            return;

        // only work when pipeline is running
        if (running)
        {
            gchar elem_name[50];
            for(int i = 0; i < totalChannelSize; ++i)
            {
                g_snprintf (elem_name, sizeof(elem_name), "sink_bin%d", i);
                auto sink_bin = gst_bin_get_by_name(GST_BIN(mainPipeline), elem_name);
                g_snprintf (elem_name, sizeof(elem_name), "sink_ivaplugin1_%d", i);
                auto ivaplugin1 = gst_bin_get_by_name(GST_BIN(sink_bin), elem_name);

                if(index == -1)  // reset all
                {
                    g_object_set(G_OBJECT(ivaplugin1), "detecting", 0, NULL);
                    if(i == totalChannelSize - 1)
                    {
                        IVA_LOG_INFO("reset all channel");
                    }
                }
                else
                {
                    if(index == i)
                    {
                        g_object_set(G_OBJECT(ivaplugin1), "detecting", 0, NULL);
                        IVA_LOG_INFO("reset channel, channel_index:{}", i);
                        break;
                    }
                }
            }
        }
    }

    /**
     * @brief 消息总线回调处理
     */
    gboolean busCall(GstBus * bus, GstMessage * msg, gpointer data)
    {
        GMainLoop* loop = (GMainLoop*)data;
        switch (GST_MESSAGE_TYPE (msg)) {
            case GST_MESSAGE_EOS:
            {
                IVA_LOG_WARN("End of stream\n");
                g_main_loop_quit (loop);
                break;
            }
            // case GST_MESSAGE_WARNING:
            // {
            //     gchar *debug;
            //     GError *error;
            //     gst_message_parse_warning(msg, &error, &debug);
            //     IVA_LOG_WARN("WARNING from element {}: {}\n", GST_OBJECT_NAME(msg->src), error->message);
            //     g_printerr("Warning: %s\n", error->message);
            //
            //     if (strstr(error->message, "Could not decode"))
            //     {
            //         std::string element_name = GST_OBJECT_NAME(msg->src);
            //     	if (strstr(element_name.c_str(), "rtph264_depay") == nullptr)
            //     	{
            //     		size_t last_index = element_name.find_last_not_of("**********");
            //     		std::string stream_id_str = element_name.substr(last_index + 1);
            //     		if (!stream_id_str.empty())
            //     		{
            //     			try {
            //     				const int channel = std::stoi(stream_id_str);
            //     				if (channel >= 0 && channel < totalChannelSize)
            //     					resetDecoder(channel);
            //     				else
            //     					IVA_LOG_WARN("Invalid channel number {} from element {}", channel, element_name);
            //     			}
            //     			catch (const std::exception& e)
            //     			{
            //     				IVA_LOG_WARN("Failed to parse channel number from element {}: {}", element_name, e.what());
            //     			}
            //     		}
            //     		else
            //     		{
            //     			IVA_LOG_WARN("Could not extract channel number from element name: {}", element_name);
            //     		}
            //     	}
            //     }
            //     g_free(debug);
            //     g_error_free(error);
            //     break;
            // }
            // case GST_MESSAGE_ERROR:
            // {
            //     bool quit(false);
            //     gchar *debug;
            //     GError *error;
            //
            //     gst_message_parse_error (msg, &error, &debug);
            //     IVA_LOG_WARN("ERROR from element {}: {}\n", GST_OBJECT_NAME(msg->src), error->message);
            //     //if (debug)
            //     IVA_LOG_INFO("Error details: {}\n", debug);
            //     if (strstr(error->message, "Failed to create NvDsInferContext"))
            //     {
            //         IVA_LOG_ERROR("\n\n模型加载失败！ 1.请检查模型是否存在且适配该显卡\n"
            //                      "               2.显卡驱动(nvidia-smi) 是否正常\n\n");
            //
            //         modelLoadFailed = true;
            //     }
            //     else if (strstr(error->message, "AiCoreException"))
            //     {
            //         quit = true;
            //         IVA_LOG_ERROR("\n\nGot aivideodecoder AiCoreException, fatal error occured, restart is need!");
            //         exit(0);
            //     }
            //     g_free(debug);
            //     g_error_free(error);
            //     // if (quit)
            //     // {
            //     //     IVA_LOG_ERROR("Got fatal error and pipeline will quit later!!!!");
            //     //     g_main_loop_quit (loop);
            //     // }
            //     //g_main_loop_quit (loop);
            //     break;
            // }
#ifdef NVIDIA
            case GST_MESSAGE_ELEMENT:
            {
                if (gst_nvmessage_is_stream_eos(msg))
                {
                    guint stream_id;
                    if (gst_nvmessage_parse_stream_eos(msg, &stream_id))
                    {
                        IVA_LOG_WARN("Got EOS from stream {}\n", stream_id);
                    }
                }
                break;
            }
#endif // !NVIDIA
            default:
                break;
        }
        return TRUE;
    }

    /**
     * @brief 请求推rtmp流
     */
    void requestRtmpStream(int index)
    {
		gchar elem_name[50];

		GstElement* sink_bin;
		GstElement* ivaplugin1;
		GstElement* encode_bin;

		if (currentRtmpStreamID >= 0)
		{
			g_snprintf(elem_name, sizeof(elem_name), "sink_bin%d", currentRtmpStreamID);
			sink_bin = gst_bin_get_by_name(GST_BIN(mainPipeline), elem_name);
			GST_ELEMENT_CHECK2(sink_bin, "sink_bin");

			g_snprintf(elem_name, sizeof(elem_name), "sink_ivaplugin1_%d", currentRtmpStreamID);
			ivaplugin1 = gst_bin_get_by_name(GST_BIN(sink_bin), elem_name);
			GST_ELEMENT_CHECK2(ivaplugin1, "ivaplugin1");
			g_object_set(G_OBJECT(ivaplugin1), "draw-frame", false, NULL);

			GST_ELEMENT_INDEX_NAME(elem_name, "encode_bin", currentRtmpStreamID);
			encode_bin = gst_bin_get_by_name(GST_BIN(sink_bin), elem_name);
			GST_ELEMENT_CHECK2(encode_bin, "encode_bin");
			gst_element_set_state(encode_bin, GST_STATE_NULL);
		}

		g_snprintf(elem_name, sizeof(elem_name), "sink_bin%d", index);
		sink_bin = gst_bin_get_by_name(GST_BIN(mainPipeline), elem_name);
		GST_ELEMENT_CHECK2(sink_bin, "sink_bin");

		g_snprintf(elem_name, sizeof(elem_name), "sink_ivaplugin1_%d", index);
		ivaplugin1 = gst_bin_get_by_name(GST_BIN(sink_bin), elem_name);
		GST_ELEMENT_CHECK2(ivaplugin1, "ivaplugin1");

		GST_ELEMENT_INDEX_NAME(elem_name, "encode_bin", index);
		encode_bin = gst_bin_get_by_name(GST_BIN(sink_bin), elem_name);
		GST_ELEMENT_CHECK2(encode_bin, "encode_bin");

		auto pad = gst_element_get_static_pad(encode_bin, "sink");
		if (!gst_pad_is_linked(pad))
		{
			gst_element_link(ivaplugin1, encode_bin);
		}
		gst_object_unref(pad);

		gst_element_set_state(encode_bin, GST_STATE_PLAYING);
		g_object_set(G_OBJECT(ivaplugin1), "draw-frame", true, NULL);
		currentRtmpStreamID = index;
    }

    /**
     * @brief rtmp和fake sink元件切换处理
     */
    void switchRtmpFakeSink(int index)
    {
        gchar elem_name[50];

        GstElement* sink_bin;
        GstElement* ivaplugin1;
        GstElement* encode_bin;
        GstElement* sink_fakesink;
        GstPad *blockpad;
        ///< 先销毁上一个结构化通道的rtmp sink相关元件
        if (currentRtmpStreamID >= 0)
        {
            g_snprintf(elem_name, sizeof(elem_name), "sink_bin%d", currentRtmpStreamID);
            sink_bin = gst_bin_get_by_name(GST_BIN(mainPipeline), elem_name);

            g_snprintf(elem_name, sizeof(elem_name), "sink_ivaplugin1_%d", currentRtmpStreamID);
            ivaplugin1 = gst_bin_get_by_name(GST_BIN(sink_bin), elem_name);
            g_object_set(G_OBJECT(ivaplugin1), "draw-frame", false, NULL);

            g_snprintf(elem_name, sizeof(elem_name), "encode_bin%d", currentRtmpStreamID);
			encode_bin = gst_bin_get_by_name(GST_BIN(sink_bin), elem_name);
			gst_element_set_state(encode_bin, GST_STATE_NULL);

            g_snprintf(elem_name, sizeof(elem_name), "sink_fakesink%d", currentRtmpStreamID);
            sink_fakesink = gst_bin_get_by_name(GST_BIN(sink_bin), elem_name);

            GstElement **datas = new GstElement* [3] { ivaplugin1, encode_bin, sink_fakesink};
            blockpad = gst_element_get_static_pad(ivaplugin1, "src");
            if (processingRtmpRequest && isRtmpRequestExpired) ///< 上一个通道可能没流，探针回调无法被调用，则手动销毁
                destroyRtmpSink(currentRtmpStreamID);
            else
                gst_pad_add_probe(blockpad, GST_PAD_PROBE_TYPE_BLOCK_DOWNSTREAM, onRtmpFakeSinkSwitch, datas, NULL);
            gst_object_unref(blockpad);
        }

        g_snprintf(elem_name, sizeof(elem_name), "sink_bin%d", index);
        sink_bin = gst_bin_get_by_name(GST_BIN(mainPipeline), elem_name);

		g_snprintf(elem_name, sizeof(elem_name), "encode_bin%d", index);
		encode_bin = gst_bin_get_by_name(GST_BIN(sink_bin), elem_name);

		if (!encode_bin)
		{
			encode_bin = createRtmpSink(index);
			GST_ELEMENT_CHECK2(encode_bin, "encode bin");
		}

        g_snprintf(elem_name, sizeof(elem_name), "sink_ivaplugin1_%d", index);
        ivaplugin1 = gst_bin_get_by_name(GST_BIN(sink_bin), elem_name);
        g_object_set(G_OBJECT(ivaplugin1), "draw-frame", true, NULL);

        g_snprintf(elem_name, sizeof(elem_name), "sink_fakesink%d", index);
        sink_fakesink = gst_bin_get_by_name(GST_BIN(sink_bin), elem_name);

        GstElement **datas = new GstElement*[3]{ ivaplugin1, sink_fakesink, encode_bin };
        blockpad = gst_element_get_static_pad(ivaplugin1, "src");
        gst_pad_add_probe(blockpad, GST_PAD_PROBE_TYPE_BLOCK_DOWNSTREAM, onRtmpFakeSinkSwitch, datas, NULL);
        gst_object_unref(blockpad);
        currentRtmpStreamID = index;
    }

    static GstPadProbeReturn onRtmpFakeSinkSwitch(GstPad * pad, GstPadProbeInfo * info, gpointer user_data)
    {
        GstElement** sinks = (GstElement**)user_data;
        GstElement* conv_before = sinks[0];
        GstElement* cur_effect = sinks[1];
        GstElement* next = sinks[2];

        gst_pad_remove_probe(pad, GST_PAD_PROBE_INFO_ID(info));

        std::string element_name = next->object.name;
        auto findFakeSink = element_name.find("fakesink");
        if (findFakeSink == std::string::npos)
            gst_element_set_state(mainPipeline, GST_STATE_PAUSED);

        gst_element_unlink(conv_before, cur_effect);
        gst_element_link(conv_before, next);

		gst_element_set_state(conv_before, GST_STATE_PLAYING);
		//gst_element_set_state(next, GST_STATE_PLAYING);

		gst_element_set_state(next, GST_STATE_PLAYING);

        size_t last_index = element_name.find_last_not_of("**********");
        string stream_id_str = element_name.substr(last_index + 1);
        int channel = std::stoi(stream_id_str);

		if (findFakeSink != std::string::npos)  //!< 由rtmp_sink ---> fake_sink
		{
			//destroyRtmpSink(channel);
			gst_element_set_state(cur_effect, GST_STATE_NULL);
			gst_bin_remove(GST_BIN(cur_effect->object.parent), cur_effect);
		}

        gst_element_set_state(mainPipeline, GST_STATE_PLAYING);

        if (findFakeSink == std::string::npos)  //!< 由fake_sink ---> rtmp_sink 检查是否还有待切换的结构化通道命令没有处理
        {
            if (latestRequestRtmpIndex == channel)
                latestRequestRtmpIndex = -1;

            if (-1 != latestRequestRtmpIndex)
            {
                auto future = std::async(std::launch::async,[&](){switchRtmpFakeSink(latestRequestRtmpIndex);});
            }
            else
            {
                processingRtmpRequest = false;
                isRtmpRequestExpired = false;
            }

        }
        delete[] sinks;

        return GST_PAD_PROBE_OK;
    }

    static GstPadProbeReturn onResetVideoCrop(GstPad * pad, GstPadProbeInfo * info, gpointer user_data)
    {
        gchar elem_name[50];
        GstElement* src_bin;
        GstElement* crop_conv;

        int* data = (int*)user_data;
        int channel = data[0];
        int x = data[1];
        int y = data[2];
        int w = data[3];
        int h = data[4];

        delete[] data;
        IVA_LOG_INFO("channel {} src crop x {} y {} w {} h {}", channel, x, y, w, h);

        g_snprintf(elem_name, sizeof(elem_name), "src_bin%d", channel);
        src_bin = gst_bin_get_by_name(GST_BIN(mainPipeline), elem_name);

        g_snprintf(elem_name, sizeof(elem_name), "crop_conv%d", channel);
        crop_conv = gst_bin_get_by_name(GST_BIN(src_bin), elem_name);

        gchar src_crop[32] = { };
        g_snprintf(src_crop, 31, "%u:%u:%u:%u", x, y, w, h);

        gst_element_set_state(crop_conv, GST_STATE_READY);
        gst_element_get_state(crop_conv, 0, 0, GST_SECOND * 5);

        g_object_set(G_OBJECT(crop_conv), "src-crop", src_crop, NULL);

        gst_element_set_state(crop_conv, GST_STATE_PLAYING);

        return GST_PAD_PROBE_REMOVE;
    }

    static GstPadProbeReturn onResetDecoderEosEvent(GstPad * pad, GstPadProbeInfo * info, gpointer user_data)
    {
        if (GST_EVENT_TYPE(GST_PAD_PROBE_INFO_DATA(info)) != GST_EVENT_EOS)
            return GST_PAD_PROBE_PASS;

        gst_pad_remove_probe(pad, GST_PAD_PROBE_INFO_ID(info));

        gchar elem_name[50];
        GstPad *srcpad, *sinkpad;
        GstElement* src_bin = (GstElement*)user_data;

        std::string element_name = src_bin->object.name;
        size_t last_index = element_name.find_last_not_of("**********");
        string stream_id_str = element_name.substr(last_index + 1);
        int channel = std::stoi(stream_id_str);

        g_snprintf(elem_name, sizeof(elem_name), "rtpdepay_capsfilter%d", channel);
        GstElement* rtpdepay_capsfilter = gst_bin_get_by_name(GST_BIN(src_bin), elem_name);

        g_snprintf(elem_name, sizeof(elem_name), "src_udp_queue%d", channel);
        GstElement* src_udp_queue = gst_bin_get_by_name(GST_BIN(src_bin), elem_name);

        g_snprintf(elem_name, sizeof(elem_name), "rtph264_depay%d", channel);
        GstElement* rtph264_depay = gst_bin_get_by_name(GST_BIN(src_bin), elem_name);

        // new depay
        GstElement* rtph264_depay_new = gst_element_factory_make("rtph264depay", elem_name);
        if (!rtph264_depay_new)
        {
            NVGSTDS_ERR_MSG_V("Failed to create '%s'", "rtph264depay");
            return GST_PAD_PROBE_DROP;
        }
        // remove original depay
        gst_bin_remove(GST_BIN(src_bin), rtph264_depay);
        gst_bin_add(GST_BIN(src_bin), rtph264_depay_new);

		

        // link all elements
        if (!gst_element_link_many(rtpdepay_capsfilter, rtph264_depay_new, src_udp_queue, NULL))
        {
            g_printerr("Elements could not be linked. Exiting.\n");
            return GST_PAD_PROBE_DROP;
        }
        gst_element_set_state(rtph264_depay_new, GST_STATE_PLAYING);

        return GST_PAD_PROBE_DROP;
    }

    static GstPadProbeReturn onResetDecoderSendEos(GstPad * pad, GstPadProbeInfo * info, gpointer user_data)
    {
        gchar elem_name[50];
        GstPad *srcpad, *sinkpad;
        GstElement* src_bin = (GstElement*)user_data;

        std::string element_name = src_bin->object.name;
        size_t last_index = element_name.find_last_not_of("**********");
        string stream_id_str = element_name.substr(last_index + 1);
        int channel = std::stoi(stream_id_str);

        g_snprintf(elem_name, sizeof(elem_name), "rtpdepay_capsfilter%d", channel);
        GstElement* rtpdepay_capsfilter = gst_bin_get_by_name(GST_BIN(src_bin), elem_name);

        g_snprintf(elem_name, sizeof(elem_name), "rtph264_depay%d", channel);
        GstElement* rtph264_depay = gst_bin_get_by_name(GST_BIN(src_bin), elem_name);

        g_snprintf(elem_name, sizeof(elem_name), "src_udp_queue%d", channel);
        GstElement* src_udp_queue = gst_bin_get_by_name(GST_BIN(src_bin), elem_name);

        /* remove the probe first */
        gst_pad_remove_probe(pad, GST_PAD_PROBE_INFO_ID(info));

        /* install new probe for EOS */
        srcpad = gst_element_get_static_pad(rtph264_depay, "src");
        gst_pad_add_probe(srcpad, GST_PAD_PROBE_TYPE_BLOCK_DOWNSTREAM, onResetDecoderEosEvent, user_data, NULL);
        gst_object_unref(srcpad);

        /* push EOS into the element, the probe will be fired when the
         * EOS leaves the effect and it has thus drained all of its data */
        sinkpad = gst_element_get_static_pad(rtph264_depay, "sink");
        gst_pad_send_event(sinkpad, gst_event_new_eos());
        gst_object_unref(sinkpad);

        return GST_PAD_PROBE_OK;
    }

#if defined(NVIDIA) && defined(USE_VIDEO_RECORD)
    static void linkRecordBin(guint index, GstElement* srcBin, GstElement* tee)
    {
        NvDsSRInitParams params{};
        params.containerType = NVDSSR_CONTAINER_MP4;
        params.cacheSize = record::CACHE_SIZE_SEC;
        params.defaultDuration = record::SMART_REC_DEFAULT_DURATION;

        guint channelId = index + 1 + (processId - 1) * totalChannelSize;
        gchar file_prefix [50];
        g_snprintf (file_prefix, sizeof(file_prefix), "iva_record_%d", channelId);
        params.fileNamePrefix = file_prefix;//!< 录像文件名前缀

        std::string date = get_system_short_time_str(get_system_timestamp());
        std::string relativePath = std::string("/searchvideo") + "/" + date;
        std::string fullPath = std::string(WEB_SERVER_ROOT) + relativePath;
        protocol::createPath(fullPath + "/");

        gchar dir_path[50];
        g_snprintf (dir_path, sizeof(dir_path), "%s", fullPath.c_str());
        params.dirpath = dir_path;

        record::create(index, recordBeforeTime, recordAfterTime, relativePath, params);

        gchar elem_name[50];
        g_snprintf(elem_name, sizeof(elem_name), "parser%d", index);
        auto h264parse = gst_element_factory_make ("h264parse", elem_name);

        auto videoRecordPtr = record::getVideoRecord(index);
        if (videoRecordPtr)
        {
            auto recordBin = videoRecordPtr->getRecordBin();
            if (recordBin)
            {
                gst_bin_add_many (GST_BIN (srcBin), h264parse, recordBin, NULL);
                if (!gst_element_link_many(tee, h264parse, recordBin, NULL))
                    IVA_LOG_ERROR("Elements not linked. Exiting.")
            }
        }
    }
#endif // NVIDIA && USE_VIDEO_RECORD

    /**
     * @brief 设置视频录制时间配置参数
     * @param[in] beforeTime 相对于当前时间的预录像时间
     * @param[in] afterTime  相对于当前时间的持续录像时间
     */
    void setIvaRecordTime(uint beforeTime, uint afterTime)
    {
        recordBeforeTime = beforeTime;
        recordAfterTime = afterTime;
    }

	/**
	* @brief 获取总通道数量
	*/
    int getTotalChannelSize()
    {
        return totalChannelSize;
    }
}
